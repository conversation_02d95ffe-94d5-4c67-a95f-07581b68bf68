<!--
  projects.html — Projects App for Windows XP Simulation
  Provides the My Projects window content and structure.
  Loaded as an iframe in the main shell.
  @file src/apps/projects/projects.html
-->
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>My Projects</title>
    <link
      rel="stylesheet"
      href="https://unpkg.com/xp.css@0.2.6/dist/XP.css"
      crossorigin="anonymous"
    />
    <link rel="stylesheet" href="projects.css" />
    <link rel="stylesheet" href="projectpage.css" />

    <!-- Wix Madefor Display Font Import -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Wix+Madefor+Display:wght@400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Work Sans Font Import -->
    <link
      href="https://fonts.googleapis.com/css2?family=Work+Sans:wght@500;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="client-work-label-fixed">Client Work</div>
    <div class="personal-work-label-fixed">Personal Work</div>
    <!-- ===== Aurora Background Effect ===== -->
    <div class="aurora">
      <div class="aurora-blob blob1"></div>
      <div class="aurora-blob blob2"></div>
      <div class="aurora-blob blob3"></div>
      <div class="aurora-blob blob4"></div>
      <div class="aurora-blob blob5"></div>
    </div>

    <!-- ===== Main Projects Grid View ===== -->
    <div id="projects-grid-view" class="main-flex-container">
      <div class="vertical-label left">Client Work</div>
      <div class="scroll-content">
        <!-- ===== Project Feed Container (Dynamically Populated) ===== -->
        <div class="feed-container">
          <!-- Project posts will be dynamically generated by projects.js -->
        </div>
      </div>
      <div class="vertical-label right">Personal Work</div>
    </div>

    <!-- ===== Project Detail View ===== -->
    <div id="project-detail-view" class="project-detail-container" style="display: none;">
      <div class="project-detail-content">
        <!-- Project media container -->
        <div class="project-media-container">
          <div class="project-media-wrapper">
            <!-- First item from Marky images array -->
            <img src="../../../assets/apps/projects/marky/marky-thumb.webp" alt="Video Clipper - Image 1">
          </div>
          <!-- Project navigation dots (similar to grid hover dots) -->
          <div class="project-detail-nav-dots">
            <span class="project-detail-nav-dot active"></span>
            <span class="project-detail-nav-dot"></span>
            <span class="project-detail-nav-dot"></span>
            <span class="project-detail-nav-dot"></span>
            <span class="project-detail-nav-dot"></span>
          </div>
          <!-- Media navigation dots (for multi-media projects) -->
          <div class="media-navigation-dots" style="display: none;">
            <!-- Dynamic content: navigation dots will be inserted here -->
          </div>
        </div>

        <!-- Project info container (text content) -->
        <div class="project-info-container">
          <div class="project-header">
            <h1 class="project-title">Video Clipper</h1>
          </div>
          <div class="project-description">
            A custom desktop app built for clipping video with speed and precision. Marky lets you import or download footage, scrub cleanly, mark in and out points, and export clips fast. Designed with a minimal UI and built for real workflows using local tools and a focused feature set.
          </div>
        </div>
      </div>
    </div>

    <!-- ===== App Interactivity Script (Module) ===== -->
    <script type="module" src="projects.js" defer></script>
  </body>
</html>
