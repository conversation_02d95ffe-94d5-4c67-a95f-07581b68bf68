/*
 * projectpage.css — Styles for Project Detail Pages
 * Contains all styles related to the project detail view
 * @file src/apps/projects/projectpage.css
 */

/* ===== DEBUG BORDERS FOR PROJECT DETAIL ===== */
.project-detail-container {
  border: 4px solid red !important;
}

.project-detail-content {
  border: 3px solid blue !important;
}

.project-media-container {
  border: 3px solid green !important;
}

.project-media-wrapper {
  border: 2px solid orange !important;
}

.project-info-container {
  border: 3px solid purple !important;
}

.project-header {
  border: 2px solid cyan !important;
}

.project-title {
  border: 1px solid yellow !important;
}

.project-description {
  border: 1px solid pink !important;
}

/* Carousel Debug Borders */
.media-carousel {
  border: 2px solid lime !important;
}

.carousel-track {
  border: 2px solid magenta !important;
}

.carousel-item {
  border: 1px solid teal !important;
}

.carousel-dots {
  border: 1px solid gold !important;
}

.carousel-dot {
  border: 1px solid lightblue !important;
}

.media-navigation-dots {
  border: 1px solid lightgreen !important;
}

.media-nav-dot {
  border: 1px solid lightcoral !important;
}

.project-detail-nav-dots {
  border: 2px solid hotpink !important;
}

.project-detail-nav-dot {
  border: 1px solid violet !important;
}

/* Main container for project detail view */
.project-detail-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(to bottom, #0a0f1a, #0b1a24 60%, #0a1a1f 100%);
  z-index: 1000;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow-y: scroll;
  overflow-x: hidden;
}

/* Hide client work labels when project detail view is visible */
body:has(.project-detail-container.visible) .client-work-label-fixed,
body:has(.project-detail-container.visible) .personal-work-label-fixed {
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.project-detail-container.visible {
  opacity: 1;
  pointer-events: auto;
}

/* Content wrapper for project detail */
.project-detail-content {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px;
  box-sizing: border-box;
  gap: 40px;
  align-items: center;
}

/* Two column layout (1200px+) - media left, copy right */
@media (min-width: 1200px) {
  .project-detail-content {
    flex-direction: row;
    max-width: 1600px;
    gap: 80px;
    align-items: flex-start;
  }

  .project-media-container {
    flex: 1; /* Takes remaining space */
    order: 1; /* Media on the left */
    min-width: 0; /* Allows shrinking */
  }

  .project-info-container {
    flex: 0 0 400px; /* Fixed width for text */
    order: 2; /* Text on the right */
    max-width: none;
  }
}

/* Media container - consolidated */
.project-media-container {
  flex: 1;
  width: 100%;
  max-width: 800px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 20px;
  perspective: 1200px;
}

/* In 2-column layout, adjust media container */
@media (min-width: 1200px) {
  .project-media-container {
    max-width: none;
    height: 80vh; /* Fixed height for 2-column layout */
    min-height: 60vh;
  }

  .project-media-wrapper {
    height: 100%;
  }

  .project-media-wrapper img,
  .project-media-wrapper video {
    max-height: 60vh; /* Slightly less than container height */
  }

  .media-carousel {
    height: 100%;
  }
}

.project-media-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  overflow-y: auto;
  padding: 20px 0;
}

.project-media-wrapper img,
.project-media-wrapper video {
  max-width: 100%;
  max-height: 60vh;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  flex-shrink: 0;
  margin-bottom: 10px;
}

/* 3D Carousel Styles */
.media-carousel {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-track {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
}

.carousel-item {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
  transform-origin: center center;
}

.carousel-item img,
.carousel-item video {
  max-width: 90%;
  max-height: 90%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* Active item (front and center) */
.carousel-item.active {
  transform: translateZ(0px) scale(1);
  z-index: 3;
  opacity: 1;
}

/* Previous item (left side, going back) */
.carousel-item.prev {
  transform: translateX(-60%) translateZ(-200px) rotateY(25deg) scale(0.8);
  z-index: 2;
  opacity: 0.7;
}

/* Next item (right side, going back) */
.carousel-item.next {
  transform: translateX(60%) translateZ(-200px) rotateY(-25deg) scale(0.8);
  z-index: 2;
  opacity: 0.7;
}

/* Hidden items (far back) */
.carousel-item.hidden {
  transform: translateZ(-400px) scale(0.5);
  z-index: 1;
  opacity: 0.3;
}

/* Carousel Navigation Dots */
.carousel-dots {
  position: absolute;
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
  z-index: 10;
}

.carousel-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
}

.carousel-dot:hover {
  border-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.2);
}

.carousel-dot.active {
  background: #ffffff;
  border-color: #ffffff;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Media navigation dots */
.media-navigation-dots {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 20px;
}

.media-nav-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: background 0.2s ease;
}

.media-nav-dot.active {
  background: rgba(255, 255, 255, 0.8);
}

.media-nav-dot:hover {
  background: rgba(255, 255, 255, 0.6);
}

/* Project Detail Navigation Dots (similar to grid hover dots) */
.project-detail-nav-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 20px 0 0 0;
  flex-shrink: 0;
}

.project-detail-nav-dot {
  width: clamp(8px, 1.5vw, 12px); /* Slightly larger than grid dots */
  height: clamp(8px, 1.5vw, 12px);
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.6);
  margin: 0 clamp(3px, 0.5vw, 5px);
  cursor: pointer;
  transition: all 0.3s ease;
}

.project-detail-nav-dot:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.1);
}

.project-detail-nav-dot.active {
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

/* Project info container - contains title and description */
.project-info-container {
  width: 100%;
  max-width: 800px;
  text-align: left;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.project-header {
  margin: 0;
  text-align: left;
  width: 100%;
}

.project-title {
  font-family: "Wix Madefor Display", sans-serif;
  font-size: 3rem;
  font-weight: 700;
  margin: 0;
  color: #ffffff;
  line-height: 1.2;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

.project-description {
  font-family: "Work Sans", sans-serif;
  font-size: 1.1rem;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin: 0;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

/* Tablet responsive adjustments */
@media (max-width: 1199px) {
  .project-detail-content {
    flex-direction: column;
    padding: 30px;
    gap: 40px;
    min-height: auto;
    align-items: center;
  }

  .project-media-container {
    width: 100%;
    max-width: 800px; /* Same as base max-width */
    padding-top: 0;
  }

  .project-info-container {
    width: 100%;
    max-width: 800px; /* Match media container width */
  }

  .project-title {
    font-size: 2.5rem;
  }
}

/* Responsive adjustments for project detail nav dots */
@media (min-width: 768px) {
  .project-detail-nav-dots {
    margin: clamp(16px, 2.5vw, 24px) 0 0 0;
  }
}

@media (max-width: 768px) {
  .project-detail-container {
    /* Ensure smooth scrolling on mobile */
    -webkit-overflow-scrolling: touch;
  }

  .project-detail-content {
    padding: 20px;
    gap: 30px;
    padding-bottom: 40px; /* Extra bottom padding for mobile scrolling */
  }

  .project-media-wrapper img,
  .project-media-wrapper video {
    max-height: 40vh;
  }

  .project-header {
    margin-bottom: 20px;
  }

  .project-title {
    font-size: 2rem;
  }

  .project-description {
    font-size: 1rem;
  }

  .project-navigation-dots {
    gap: 4px;
  }

  .project-nav-dot {
    width: 8px;
    height: 8px;
  }

  /* Mobile styling for project detail nav dots */
  .project-detail-nav-dots {
    margin: 12px 0 0 0;
  }

  .project-detail-nav-dot {
    width: 10px !important;
    height: 10px !important;
    margin: 0 3px;
  }
}
