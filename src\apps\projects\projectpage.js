/*
 * projectpage.js - Handles project detail page functionality
 */

// ===== Module Imports ===== //
import { isMobileDevice } from "../../scripts/utils/device.js";

// ===== Utility Functions ===== //

/**
 * Helper function to create a DOM element with optional class and text content.
 * @param {string} tag - The HTML tag for the element.
 * @param {string | null} className - Optional class name to add to the element.
 * @param {string} [text] - Optional text content for the element.
 * @returns {HTMLElement} The created DOM element.
 */
function createEl(tag, className, text) {
  const el = document.createElement(tag);
  if (className) el.className = className;
  if (text !== undefined) el.textContent = text;
  return el;
}

/**
 * Converts a relative asset path to an absolute path from the main application root.
 * @param {string} path - The relative path to convert.
 * @returns {string} The absolute path, or the original path if already absolute.
 */
function toAbsoluteAssetPath(path) {
  if (!path) return path;
  if (path.startsWith("http:") || path.startsWith("https:")) return path;
  if (path.startsWith("../")) return path;
  return "../../../" + path; // Path relative to projects app directory
}

/**
 * Sends a message to the parent window if the current window is an iframe.
 * @param {Object} payload - The message payload to send.
 */
function sendMessageToParent(payload) {
  if (window.parent && window.parent !== window) {
    window.parent.postMessage(payload, "*");
  }
}

// ===== Global State ===== //
// (State variables are managed in projects.js)

// ===== Project Detail Functions ===== //

/**
 * Gets media items for a project in the correct format
 * @param {Object} project - Project data object
 * @returns {Array} Array of media items
 */
function getProjectMediaItems(project) {
  const mediaItems = [];

  // Use the images array as the primary source for media items
  if (project.images && Array.isArray(project.images)) {
    project.images.forEach((item) => {
      if (typeof item === "string") {
        const mediaItem = {
          type: "image",
          src: toAbsoluteAssetPath(item),
          poster: null
        };
        mediaItems.push(mediaItem);
      } else if (typeof item === "object" && item.src) {
        const isMobile = isMobileDevice();
        const mediaItem = {
          type: item.type || "video",
          src: isMobile && item.srcMobile ? toAbsoluteAssetPath(item.srcMobile) : toAbsoluteAssetPath(item.src),
          poster: null
        };

        if (mediaItem.type === "video" && item.poster) {
          mediaItem.poster = isMobile && item.posterMobile ?
            toAbsoluteAssetPath(item.posterMobile) :
            toAbsoluteAssetPath(item.poster);
        }

        mediaItems.push(mediaItem);
      }
    });
  }

  // If no images array, fall back to main src
  if (mediaItems.length === 0 && project.src) {
    const isMobile = isMobileDevice();
    const mainMedia = {
      type: project.type || "image",
      src: isMobile && project.srcMobile ? toAbsoluteAssetPath(project.srcMobile) : toAbsoluteAssetPath(project.src),
      poster: null
    };

    if (mainMedia.type === "video" && project.poster) {
      mainMedia.poster = isMobile && project.posterMobile ?
        toAbsoluteAssetPath(project.posterMobile) :
        toAbsoluteAssetPath(project.poster);
    }

    mediaItems.push(mainMedia);
  }

  return mediaItems;
}

/**
 * Updates toolbar state for detail view
 * @param {Object} project - Current project data
 * @param {number} totalProjects - Total number of projects
 */
function updateToolbarForDetailView(project, totalProjects) {
  const mediaItems = getProjectMediaItems(project);
  const hasMultipleMedia = mediaItems.length > 1;

  // With wrap-around navigation, next/previous are always available if we have projects
  const canNavigateNext = totalProjects > 1 || hasMultipleMedia;
  const canNavigatePrevious = totalProjects > 1 || hasMultipleMedia;

  sendMessageToParent({
    type: "project-detail-state",
    inDetailView: true,
    hasNext: canNavigateNext,
    hasPrevious: canNavigatePrevious,
    canGoBack: true
  });
}

/**
 * Updates toolbar state for grid view
 */
function updateToolbarForGridView() {
  sendMessageToParent({
    type: "project-detail-state",
    inDetailView: false,
    hasNext: false,
    hasPrevious: false,
    canGoBack: false
  });
}

// ===== Export Functions for Projects.js ===== //
window.ProjectPage = {
  getProjectMediaItems,
  updateToolbarForDetailView,
  updateToolbarForGridView
};


