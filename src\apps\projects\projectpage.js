/*
 * projectpage.js - Handles project detail page functionality
 */

// ===== Module Imports ===== //
import { isMobileDevice } from "../../scripts/utils/device.js";

// ===== Utility Functions ===== //

/**
 * Helper function to create a DOM element with optional class and text content.
 * @param {string} tag - The HTML tag for the element.
 * @param {string | null} className - Optional class name to add to the element.
 * @param {string} [text] - Optional text content for the element.
 * @returns {HTMLElement} The created DOM element.
 */
function createEl(tag, className, text) {
  const el = document.createElement(tag);
  if (className) el.className = className;
  if (text !== undefined) el.textContent = text;
  return el;
}

/**
 * Converts a relative asset path to an absolute path from the main application root.
 * @param {string} path - The relative path to convert.
 * @returns {string} The absolute path, or the original path if already absolute.
 */
function toAbsoluteAssetPath(path) {
  if (!path) return path;
  if (path.startsWith("http:") || path.startsWith("https:")) return path;
  if (path.startsWith("../")) return path;
  return "../../../" + path; // Path relative to projects app directory
}

/**
 * Sends a message to the parent window if the current window is an iframe.
 * @param {Object} payload - The message payload to send.
 */
function sendMessageToParent(payload) {
  if (window.parent && window.parent !== window) {
    window.parent.postMessage(payload, "*");
  }
}

// ===== Global State ===== //
let allPostsData = []; // Stores all project data
let currentProjectIndex = null; // Currently displayed project index
let currentMediaIndex = 0; // Currently displayed media index within project
let isTransitioning = false; // Flag to prevent concurrent transitions

// ===== Project Detail Functions ===== //

/**
 * Gets media items for a project in the correct format
 * @param {Object} project - Project data object
 * @returns {Array} Array of media items
 */
function getProjectMediaItems(project) {
  const mediaItems = [];

  // Use the images array as the primary source for media items
  if (project.images && Array.isArray(project.images)) {
    project.images.forEach((item) => {
      if (typeof item === "string") {
        const mediaItem = {
          type: "image",
          src: toAbsoluteAssetPath(item),
          poster: null
        };
        mediaItems.push(mediaItem);
      } else if (typeof item === "object" && item.src) {
        const isMobile = isMobileDevice();
        const mediaItem = {
          type: item.type || "video",
          src: isMobile && item.srcMobile ? toAbsoluteAssetPath(item.srcMobile) : toAbsoluteAssetPath(item.src),
          poster: null
        };

        if (mediaItem.type === "video" && item.poster) {
          mediaItem.poster = isMobile && item.posterMobile ?
            toAbsoluteAssetPath(item.posterMobile) :
            toAbsoluteAssetPath(item.poster);
        }

        mediaItems.push(mediaItem);
      }
    });
  }

  // If no images array, fall back to main src
  if (mediaItems.length === 0 && project.src) {
    const isMobile = isMobileDevice();
    const mainMedia = {
      type: project.type || "image",
      src: isMobile && project.srcMobile ? toAbsoluteAssetPath(project.srcMobile) : toAbsoluteAssetPath(project.src),
      poster: null
    };

    if (mainMedia.type === "video" && project.poster) {
      mainMedia.poster = isMobile && project.posterMobile ?
        toAbsoluteAssetPath(project.posterMobile) :
        toAbsoluteAssetPath(project.poster);
    }

    mediaItems.push(mainMedia);
  }

  return mediaItems;
}

/**
 * Updates the media carousel for the current project
 * @param {Object} project - Project data object
 */
function updateProjectMedia(project) {
  populateProjectDetail(project, currentMediaIndex);
}

/**
 * Updates toolbar state for detail view
 * @param {Object} project - Current project data
 */
function updateToolbarForDetailView(project) {
  const mediaItems = getProjectMediaItems(project);
  const hasMultipleMedia = mediaItems.length > 1;

  // With wrap-around navigation, next/previous are always available if we have projects
  const canNavigateNext = allPostsData.length > 1 || hasMultipleMedia;
  const canNavigatePrevious = allPostsData.length > 1 || hasMultipleMedia;

  sendMessageToParent({
    type: "project-detail-state",
    inDetailView: true,
    hasNext: canNavigateNext,
    hasPrevious: canNavigatePrevious,
    canGoBack: true
  });
}

/**
 * Updates toolbar state for grid view
 */
function updateToolbarForGridView() {
  sendMessageToParent({
    type: "project-detail-state",
    inDetailView: false,
    hasNext: false,
    hasPrevious: false,
    canGoBack: false
  });
}

/**
 * Populates the project detail view with project data
 * @param {Object} project - Project data object
 * @param {number} mediaIndex - Index of media item to display
 */
function populateProjectDetail(project, mediaIndex = 0) {
  currentMediaIndex = mediaIndex;
  // Get media items for this project
  const mediaItems = getProjectMediaItems(project);
  const currentMedia = mediaItems[mediaIndex] || mediaItems[0];

  if (!currentMedia) {
    console.error("No media found for project:", project);
    return;
  }

  // Update media container with carousel
  const mediaWrapper = document.querySelector(".project-media-wrapper");
  if (mediaWrapper) {
    mediaWrapper.innerHTML = "";

    if (mediaItems.length === 1) {
      // Single media item - show normally
      const currentMedia = mediaItems[0];
      if (currentMedia.type === "video") {
        const video = createEl("video");
        video.src = currentMedia.src;
        video.controls = true;
        video.muted = true;
        video.setAttribute("playsinline", "");
        if (currentMedia.poster) {
          video.poster = currentMedia.poster;
        }
        mediaWrapper.appendChild(video);
      } else {
        const img = createEl("img");
        img.src = currentMedia.src;
        img.alt = project.title || "Project Image";
        mediaWrapper.appendChild(img);
      }
    } else {
      // Multiple media items - create carousel
      const carousel = createEl("div", "media-carousel");
      const carouselTrack = createEl("div", "carousel-track");

      mediaItems.forEach((media, index) => {
        const mediaItem = createEl("div", "carousel-item");

        if (media.type === "video") {
          const video = createEl("video");
          video.src = media.src;
          video.controls = true;
          video.muted = true;
          video.setAttribute("playsinline", "");
          if (media.poster) {
            video.poster = media.poster;
          }
          mediaItem.appendChild(video);
        } else {
          const img = createEl("img");
          img.src = media.src;
          img.alt = project.title || "Project Image";
          mediaItem.appendChild(img);
        }

        // Set position classes
        if (index === mediaIndex) {
          mediaItem.classList.add("active");
        } else if (index === (mediaIndex - 1 + mediaItems.length) % mediaItems.length) {
          mediaItem.classList.add("prev");
        } else if (index === (mediaIndex + 1) % mediaItems.length) {
          mediaItem.classList.add("next");
        } else {
          mediaItem.classList.add("hidden");
        }

        // Add click handlers for navigation
        if (index !== mediaIndex) {
          mediaItem.style.cursor = "pointer";
          mediaItem.addEventListener("click", () => {
            currentMediaIndex = index;
            updateProjectMedia(project);
          });
        }

        carouselTrack.appendChild(mediaItem);
      });

      carousel.appendChild(carouselTrack);

      // Add navigation dots for carousel
      const dotsContainer = createEl("div", "carousel-dots");
      mediaItems.forEach((_, index) => {
        const dot = createEl("div", "carousel-dot");
        if (index === mediaIndex) {
          dot.classList.add("active");
        }
        dot.addEventListener("click", () => {
          if (index !== currentMediaIndex) {
            currentMediaIndex = index;
            updateProjectMedia(project);
          }
        });
        dotsContainer.appendChild(dot);
      });

      carousel.appendChild(dotsContainer);
      mediaWrapper.appendChild(carousel);

      // Add keyboard navigation
      carousel.setAttribute("tabindex", "0");
      carousel.addEventListener("keydown", (e) => {
        if (e.key === "ArrowLeft") {
          e.preventDefault();
          currentMediaIndex = (currentMediaIndex - 1 + mediaItems.length) % mediaItems.length;
          updateProjectMedia(project);
        } else if (e.key === "ArrowRight") {
          e.preventDefault();
          currentMediaIndex = (currentMediaIndex + 1) % mediaItems.length;
          updateProjectMedia(project);
        }
      });
    }
  }

  // Update media navigation dots if multiple media items
  const mediaDotsContainer = document.querySelector(".media-navigation-dots");

  if (mediaDotsContainer && mediaItems.length > 1) {
    mediaDotsContainer.style.display = "flex";
    mediaDotsContainer.innerHTML = "";

    mediaItems.forEach((_, index) => {
      const dot = createEl("div", "media-nav-dot");
      if (index === mediaIndex) {
        dot.classList.add("active");
      }
      dot.addEventListener("click", () => {
        if (index !== currentMediaIndex) {
          currentMediaIndex = index;
          updateProjectMedia(allPostsData[currentProjectIndex]);
        }
      });
      mediaDotsContainer.appendChild(dot);
    });
  } else if (mediaDotsContainer) {
    mediaDotsContainer.style.display = "none";
  }

  // Update project info - only title and description
  const titleEl = document.querySelector(".project-title");
  const descriptionEl = document.querySelector(".project-description");

  if (titleEl) titleEl.textContent = project.title || "";
  if (descriptionEl) descriptionEl.textContent = project.description || "";
}

/**
 * Shows the project detail view with fade transition
 * @param {number} projectIndex - Index of the project to display
 * @param {number} mediaIndex - Index of the media item to display (default: 0)
 */
function showProjectDetail(projectIndex, mediaIndex = 0) {
  if (isTransitioning) {
    return;
  }
  isTransitioning = true;

  currentProjectIndex = projectIndex;
  currentMediaIndex = mediaIndex;

  const project = allPostsData[projectIndex];

  if (!project) {
    console.error("Project not found at index:", projectIndex);
    isTransitioning = false;
    return;
  }

  // Get DOM elements
  const gridView = document.getElementById("projects-grid-view");
  const detailView = document.getElementById("project-detail-view");

  if (!gridView || !detailView) {
    console.error("Required view elements not found");
    isTransitioning = false;
    return;
  }

  // Populate project detail content
  populateProjectDetail(project, mediaIndex);

  // Show detail view and hide grid
  detailView.style.display = "block";

  // Trigger fade transition
  requestAnimationFrame(() => {
    gridView.classList.add("hidden");
    detailView.classList.add("visible");

    // Update toolbar state
    updateToolbarForDetailView(project);

    setTimeout(() => {
      isTransitioning = false;
    }, 300);
  });
}

/**
 * Hides the project detail view and returns to grid
 */
function hideProjectDetail() {
  if (isTransitioning) return;
  isTransitioning = true;

  const gridView = document.getElementById("projects-grid-view");
  const detailView = document.getElementById("project-detail-view");

  if (!gridView || !detailView) {
    console.error("Required view elements not found");
    isTransitioning = false;
    return;
  }

  // Trigger fade transition
  gridView.classList.remove("hidden");
  detailView.classList.remove("visible");

  // Update toolbar state
  updateToolbarForGridView();

  setTimeout(() => {
    detailView.style.display = "none";
    currentProjectIndex = null;
    currentMediaIndex = 0;
    isTransitioning = false;
  }, 300);
}
