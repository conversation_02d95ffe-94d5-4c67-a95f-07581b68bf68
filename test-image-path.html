<!DOCTYPE html>
<html>
<head>
    <title>Image Path Test</title>
</head>
<body>
    <h1>Testing Image Paths</h1>
    
    <h2>From root (should work if opened from root)</h2>
    <img src="assets/apps/projects/marky/marky-thumb.webp" alt="From root" style="max-width: 200px;">
    
    <h2>From projects folder perspective (../../../)</h2>
    <img src="../../../assets/apps/projects/marky/marky-thumb.webp" alt="From projects perspective" style="max-width: 200px;">
    
    <h2>Absolute path test</h2>
    <img src="/assets/apps/projects/marky/marky-thumb.webp" alt="Absolute path" style="max-width: 200px;">
    
    <script>
        // Test if files exist
        const testPaths = [
            'assets/apps/projects/marky/marky-thumb.webp',
            '../../../assets/apps/projects/marky/marky-thumb.webp',
            '/assets/apps/projects/marky/marky-thumb.webp'
        ];
        
        testPaths.forEach((path, index) => {
            fetch(path)
                .then(response => {
                    console.log(`Path ${index + 1} (${path}): ${response.ok ? 'SUCCESS' : 'FAILED'} - Status: ${response.status}`);
                })
                .catch(error => {
                    console.log(`Path ${index + 1} (${path}): ERROR - ${error.message}`);
                });
        });
    </script>
</body>
</html>
